using UnityEngine;
using UnityEngine.UI;

public class VehiclespawnAroundPlayer : MonoBehaviour
{
    [Header("Vehicle Prefabs")]
    public GameObject bikePrefab, FiretruckPrefab, policeCarPrefab,MonsterTruckPrefab;

    [Header("Spawn Settings")]
    public Transform SpawnPosition;
    public Transform Helicopter,train, Airplane, Boat;
    public GameObject player;

    [<PERSON><PERSON>("UI Buttons")]
    public Button bikeButton, firetruckButton, policecarButton, monsterTruckButton;
    public Button helicopterButton, airplaneButton, boatButton, trainButton;

    void Start()
    {
        // Automatically assign button listeners
        if (bikeButton != null) bikeButton.onClick.AddListener(SpawnBike);
        if (firetruckButton != null) firetruckButton.onClick.AddListener(firetruck);
        if (policecarButton != null) policecarButton.onClick.AddListener(plicecar);
        if (monsterTruckButton != null) monsterTruckButton.onClick.AddListener(MonsterTruck);
        if (helicopterButton != null) helicopterButton.onClick.AddListener(SpawnHelicopter);
        if (airplaneButton != null) airplaneButton.onClick.AddListener(SpawnAirplane);
        if (boatButton != null) boatButton.onClick.AddListener(SpawnBoat);
        if (trainButton != null) trainButton.onClick.AddListener(SpawnTrain);
    }

    public void SpawnBike()
    {
        if (bikePrefab != null && SpawnPosition != null)
        {
            GameObject spawnedBike = Instantiate(bikePrefab, SpawnPosition.position, SpawnPosition.rotation);
            // Ensure the spawned bike has the correct tag for detection
            if (!spawnedBike.CompareTag("Motorbike"))
            {
                spawnedBike.tag = "Motorbike";
            }
        }
    }

    public void firetruck()
    {
        if (FiretruckPrefab != null && SpawnPosition != null)
        {
            GameObject spawnedFiretruck = Instantiate(FiretruckPrefab, SpawnPosition.position, SpawnPosition.rotation);
            // Ensure the spawned firetruck has the correct tag for detection
            if (!spawnedFiretruck.CompareTag("Vehicle"))
            {
                spawnedFiretruck.tag = "Vehicle";
            }
        }
    }

    public void plicecar()
    {
        if (policeCarPrefab != null && SpawnPosition != null)
        {
            GameObject spawnedPoliceCar = Instantiate(policeCarPrefab, SpawnPosition.position, SpawnPosition.rotation);
            // Ensure the spawned police car has the correct tag for detection
            if (!spawnedPoliceCar.CompareTag("Vehicle"))
            {
                spawnedPoliceCar.tag = "Vehicle";
            }
        }
    }

    public void MonsterTruck()
    {
        if (MonsterTruckPrefab != null && SpawnPosition != null)
        {
            GameObject spawnedMonsterTruck = Instantiate(MonsterTruckPrefab, SpawnPosition.position, SpawnPosition.rotation);
            // Ensure the spawned monster truck has the correct tag for detection
            if (!spawnedMonsterTruck.CompareTag("Vehicle"))
            {
                spawnedMonsterTruck.tag = "Vehicle";
            }
        }
    }

    public void SpawnHelicopter()
    {
        if (player != null && Helicopter != null)
        {
            // Move player to helicopter position
            player.transform.position = Helicopter.position;
            player.transform.rotation = Helicopter.rotation;
        }
    }

    public void SpawnAirplane()
    {
        if (player != null && Airplane != null)
        {
            // Move player to airplane position
            player.transform.position = Airplane.position;
            player.transform.rotation = Airplane.rotation;
        }
    }

    public void SpawnBoat()
    {
        if (player != null && Boat != null)
        {
            // Move player to boat position
            player.transform.position = Boat.position;
            player.transform.rotation = Boat.rotation;
        }
    }

    public void SpawnTrain()
    {
        if (player != null && train != null)
        {
            // Move player to train position
            player.transform.position = train.position;
            player.transform.rotation = train.rotation;
        }
    }

}
